#!/usr/bin/env python3
"""
Example usage of the MCP CLI Client

This script demonstrates how to use the MCP CLI client programmatically
and provides examples of different usage patterns.
"""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path

from mcp_cli_client import MC<PERSON><PERSON>, MCPConfig, ScheduleTourRequest


async def example_basic_usage():
    """Basic example of using the MCP client."""
    print("=== Basic MCP Client Usage ===")
    
    # Create configuration
    config = MCPConfig(
        server_url="http://localhost:8000",
        mcp_path="/mcp",
        timeout=30,
        log_level="INFO"
    )
    
    # Use the client
    async with MCPClient(config) as client:
        # List available tools
        tools = client.list_tools()
        print(f"Available tools: {tools}")
        
        # Get tool information
        if "schedule_property_tour" in tools:
            tool_info = client.get_tool_info("schedule_property_tour")
            print(f"Schedule tour tool: {tool_info}")


async def example_schedule_tour():
    """Example of scheduling a tour."""
    print("\n=== Schedule Tour Example ===")
    
    config = MCPConfig()
    
    # Create tour request
    tour_date = datetime.now() + timedelta(days=7)  # One week from now
    request = ScheduleTourRequest(
        property_id=12345,
        renter_id=67890,
        tour_date=tour_date,
        first_name="John",
        last_name="Doe",
        preference_bedrooms=2,
        preference_move_date="2025-01-15"
    )
    
    async with MCPClient(config) as client:
        try:
            response = await client.schedule_tour(request)
            print(f"Tour scheduled successfully!")
            print(f"Status: {response.status}")
            print(f"Appointment ID: {response.appointment_id}")
            print(f"Message: {response.message}")
        except Exception as e:
            print(f"Failed to schedule tour: {e}")


async def example_error_handling():
    """Example of error handling."""
    print("\n=== Error Handling Example ===")
    
    config = MCPConfig(
        server_url="http://localhost:9999",  # Wrong port
        timeout=5,
        max_retries=1
    )
    
    try:
        async with MCPClient(config) as client:
            tools = client.list_tools()
            print(f"Tools: {tools}")
    except Exception as e:
        print(f"Expected error (wrong server): {e}")


async def example_configuration_from_file():
    """Example of loading configuration from file."""
    print("\n=== Configuration from File Example ===")
    
    # Create a sample config file
    config_data = {
        "server_url": "http://localhost:8000",
        "mcp_path": "/mcp",
        "timeout": 30,
        "max_retries": 3,
        "log_level": "DEBUG"
    }
    
    config_file = Path("example_config.json")
    with open(config_file, "w") as f:
        json.dump(config_data, f, indent=2)
    
    print(f"Created config file: {config_file}")
    
    # Load configuration from file
    with open(config_file, "r") as f:
        loaded_config = json.load(f)
    
    config = MCPConfig(**loaded_config)
    print(f"Loaded config: server_url={config.server_url}, log_level={config.log_level}")
    
    # Clean up
    config_file.unlink()


async def example_batch_operations():
    """Example of performing multiple operations."""
    print("\n=== Batch Operations Example ===")
    
    config = MCPConfig(log_level="WARNING")  # Reduce log noise
    
    async with MCPClient(config) as client:
        # Schedule multiple tours
        base_date = datetime.now() + timedelta(days=1)
        
        tours = [
            {
                "property_id": 12345,
                "renter_id": 67890,
                "tour_date": base_date + timedelta(hours=i),
                "first_name": f"User{i}",
                "last_name": "Test",
                "preference_bedrooms": 1 + (i % 3)
            }
            for i in range(3)
        ]
        
        results = []
        for i, tour_data in enumerate(tours):
            try:
                request = ScheduleTourRequest(**tour_data)
                response = await client.schedule_tour(request)
                results.append(f"Tour {i+1}: {response.status} - {response.appointment_id}")
            except Exception as e:
                results.append(f"Tour {i+1}: Failed - {e}")
        
        print("Batch results:")
        for result in results:
            print(f"  {result}")


async def main():
    """Run all examples."""
    print("MCP CLI Client Examples")
    print("=" * 50)
    
    try:
        await example_basic_usage()
        await example_schedule_tour()
        await example_error_handling()
        await example_configuration_from_file()
        await example_batch_operations()
        
        print("\n" + "=" * 50)
        print("All examples completed!")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
