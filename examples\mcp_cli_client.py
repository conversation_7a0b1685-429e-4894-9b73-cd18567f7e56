#!/usr/bin/env python3
"""
MCP CLI Client for Schedule Tour Tool

A production-ready Python CLI client that connects to an MCP server via HTTP transport
and provides access to the schedule tour functionality.

Features:
- Async/await patterns with aiohttp for HTTP transport
- Comprehensive error handling and logging
- Configuration via CLI args, environment variables, and config files
- Input validation with Pydantic models
- Interactive and non-interactive modes
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import aiohttp
import click
from pydantic import BaseModel, Field, ValidationError
from pydantic_settings import BaseSettings


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("mcp_client.log")
    ]
)
logger = logging.getLogger(__name__)


class MCPError(Exception):
    """Base exception for MCP-related errors."""
    pass


class MCPConnectionError(MCPError):
    """Raised when connection to MCP server fails."""
    pass


class MCPProtocolError(MCPError):
    """Raised when MCP protocol communication fails."""
    pass


class MCPToolError(MCPError):
    """Raised when tool execution fails."""
    pass


class MCPConfig(BaseSettings):
    """Configuration settings for the MCP client."""
    
    server_url: str = Field(default="http://localhost:8000", description="MCP server URL")
    mcp_path: str = Field(default="/mcp", description="MCP endpoint path")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum number of retries")
    log_level: str = Field(default="INFO", description="Logging level")
    
    class Config:
        env_prefix = "MCP_"
        env_file = ".env"


class ScheduleTourRequest(BaseModel):
    """Request model for scheduling a property tour."""
    
    property_id: int = Field(..., gt=0, description="Knock property ID")
    renter_id: int = Field(..., gt=0, description="Prospect renter ID")
    tour_date: datetime = Field(..., description="Requested tour datetime")
    first_name: str = Field(..., min_length=1, max_length=50, description="Prospect first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Prospect last name")
    preference_bedrooms: Optional[int] = Field(None, ge=0, le=4, description="Desired bedroom count (0-4)")
    preference_move_date: Optional[str] = Field(None, description="Preferred move-in date (YYYY-MM-DD)")


class ScheduleTourResponse(BaseModel):
    """Response model for tour scheduling."""
    
    status: str = Field(..., description="Status string")
    appointment_id: str = Field(..., description="Appointment identifier")
    message: str = Field(..., description="Human-readable message")


class MCPMessage(BaseModel):
    """Base MCP message structure."""
    
    jsonrpc: str = Field(default="2.0", description="JSON-RPC version")
    id: Union[str, int] = Field(..., description="Message ID")


class MCPRequest(MCPMessage):
    """MCP request message."""
    
    method: str = Field(..., description="Method name")
    params: Optional[Dict[str, Any]] = Field(default=None, description="Method parameters")


class MCPResponse(MCPMessage):
    """MCP response message."""
    
    result: Optional[Dict[str, Any]] = Field(default=None, description="Result data")
    error: Optional[Dict[str, Any]] = Field(default=None, description="Error information")


class MCPClient:
    """
    Async MCP client for communicating with MCP servers via HTTP transport.
    
    This client handles:
    - Connection management with retry logic
    - MCP protocol communication
    - Tool discovery and invocation
    - Error handling and logging
    """
    
    def __init__(self, config: MCPConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = f"{config.server_url.rstrip('/')}{config.mcp_path}"
        self.message_id = 0
        self.tools: Dict[str, Dict[str, Any]] = {}
        
        # Configure logging level
        logging.getLogger().setLevel(getattr(logging, config.log_level.upper()))
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    async def connect(self) -> None:
        """Establish connection to the MCP server."""
        try:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test connection and initialize
            await self._initialize()
            logger.info(f"Connected to MCP server at {self.base_url}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            raise MCPConnectionError(f"Connection failed: {e}") from e
    
    async def disconnect(self) -> None:
        """Close connection to the MCP server."""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Disconnected from MCP server")
    
    def _get_next_id(self) -> int:
        """Get next message ID."""
        self.message_id += 1
        return self.message_id
    
    async def _send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Send MCP request and return response."""
        if not self.session:
            raise MCPConnectionError("Not connected to MCP server")
        
        request = MCPRequest(
            id=self._get_next_id(),
            method=method,
            params=params
        )
        
        for attempt in range(self.config.max_retries):
            try:
                logger.debug(f"Sending MCP request (attempt {attempt + 1}): {request.model_dump()}")
                
                async with self.session.post(
                    self.base_url,
                    json=request.model_dump(),
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status != 200:
                        raise MCPProtocolError(f"HTTP {response.status}: {await response.text()}")
                    
                    response_data = await response.json()
                    logger.debug(f"Received MCP response: {response_data}")
                    
                    mcp_response = MCPResponse(**response_data)
                    
                    if mcp_response.error:
                        raise MCPProtocolError(f"MCP error: {mcp_response.error}")
                    
                    return mcp_response.result or {}
                    
            except aiohttp.ClientError as e:
                logger.warning(f"Request attempt {attempt + 1} failed: {e}")
                if attempt == self.config.max_retries - 1:
                    raise MCPConnectionError(f"Request failed after {self.config.max_retries} attempts: {e}") from e
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        raise MCPConnectionError("Max retries exceeded")
    
    async def _initialize(self) -> None:
        """Initialize the MCP connection and discover tools."""
        try:
            # Initialize the connection
            await self._send_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "mcp-cli-client",
                    "version": "1.0.0"
                }
            })
            
            # Discover available tools
            await self._discover_tools()
            
        except Exception as e:
            logger.error(f"MCP initialization failed: {e}")
            raise MCPProtocolError(f"Initialization failed: {e}") from e
    
    async def _discover_tools(self) -> None:
        """Discover available tools from the MCP server."""
        try:
            result = await self._send_request("tools/list")
            tools_list = result.get("tools", [])
            
            self.tools = {tool["name"]: tool for tool in tools_list}
            logger.info(f"Discovered {len(self.tools)} tools: {list(self.tools.keys())}")
            
        except Exception as e:
            logger.error(f"Tool discovery failed: {e}")
            raise MCPProtocolError(f"Tool discovery failed: {e}") from e
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a specific tool with the given arguments."""
        if tool_name not in self.tools:
            available_tools = list(self.tools.keys())
            raise MCPToolError(f"Tool '{tool_name}' not found. Available tools: {available_tools}")
        
        try:
            logger.info(f"Calling tool '{tool_name}' with arguments: {arguments}")
            
            result = await self._send_request("tools/call", {
                "name": tool_name,
                "arguments": arguments
            })
            
            logger.info(f"Tool '{tool_name}' executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Tool '{tool_name}' execution failed: {e}")
            raise MCPToolError(f"Tool execution failed: {e}") from e
    
    async def schedule_tour(self, request: ScheduleTourRequest) -> ScheduleTourResponse:
        """Schedule a property tour using the MCP server."""
        try:
            # Convert datetime to ISO format for JSON serialization
            arguments = request.model_dump()
            arguments["tour_date"] = request.tour_date.isoformat()
            
            result = await self.call_tool("schedule_property_tour", arguments)
            
            # Extract the tool result content
            if "content" in result and result["content"]:
                content = result["content"][0]
                if content.get("type") == "text":
                    # Parse the JSON response from the tool
                    response_data = json.loads(content["text"])
                    return ScheduleTourResponse(**response_data)
            
            raise MCPToolError("Invalid response format from schedule_property_tour tool")
            
        except ValidationError as e:
            logger.error(f"Response validation failed: {e}")
            raise MCPToolError(f"Invalid response format: {e}") from e
        except Exception as e:
            logger.error(f"Tour scheduling failed: {e}")
            raise MCPToolError(f"Tour scheduling failed: {e}") from e
    
    def list_tools(self) -> List[str]:
        """Get list of available tools."""
        return list(self.tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool."""
        return self.tools.get(tool_name)


# CLI Implementation
@click.group()
@click.option("--server-url", default="http://localhost:8000", help="MCP server URL")
@click.option("--mcp-path", default="/mcp", help="MCP endpoint path")
@click.option("--timeout", default=30, help="Request timeout in seconds")
@click.option("--max-retries", default=3, help="Maximum number of retries")
@click.option("--log-level", default="INFO", help="Logging level")
@click.option("--config-file", type=click.Path(), help="Configuration file path")
@click.pass_context
def cli(ctx, server_url, mcp_path, timeout, max_retries, log_level, config_file):
    """MCP CLI Client for Schedule Tour Tool."""

    # Load configuration
    config_data = {}

    # Load from config file if provided
    if config_file and Path(config_file).exists():
        try:
            with open(config_file, 'r') as f:
                config_data = json.load(f)
        except Exception as e:
            click.echo(f"Warning: Failed to load config file: {e}", err=True)

    # Override with CLI arguments
    config_data.update({
        "server_url": server_url,
        "mcp_path": mcp_path,
        "timeout": timeout,
        "max_retries": max_retries,
        "log_level": log_level
    })

    try:
        config = MCPConfig(**config_data)
        ctx.ensure_object(dict)
        ctx.obj["config"] = config
    except ValidationError as e:
        click.echo(f"Configuration error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
async def list_tools(ctx):
    """List available tools from the MCP server."""
    config = ctx.obj["config"]

    try:
        async with MCPClient(config) as client:
            tools = client.list_tools()

            if not tools:
                click.echo("No tools available.")
                return

            click.echo("Available tools:")
            for tool_name in tools:
                tool_info = client.get_tool_info(tool_name)
                description = tool_info.get("description", "No description") if tool_info else "No description"
                click.echo(f"  • {tool_name}: {description}")

    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option("--property-id", required=True, type=int, help="Property ID")
@click.option("--renter-id", required=True, type=int, help="Renter ID")
@click.option("--tour-date", required=True, help="Tour date and time (YYYY-MM-DD HH:MM)")
@click.option("--first-name", required=True, help="First name")
@click.option("--last-name", required=True, help="Last name")
@click.option("--bedrooms", type=int, help="Preferred number of bedrooms (0-4)")
@click.option("--move-date", help="Preferred move-in date (YYYY-MM-DD)")
@click.option("--interactive", is_flag=True, help="Interactive mode")
@click.pass_context
async def schedule_tour(ctx, property_id, renter_id, tour_date, first_name, last_name, bedrooms, move_date, interactive):
    """Schedule a property tour."""
    config = ctx.obj["config"]

    try:
        # Interactive mode
        if interactive:
            property_id = click.prompt("Property ID", type=int, default=property_id if property_id else None)
            renter_id = click.prompt("Renter ID", type=int, default=renter_id if renter_id else None)
            tour_date = click.prompt("Tour date and time (YYYY-MM-DD HH:MM)", default=tour_date if tour_date else None)
            first_name = click.prompt("First name", default=first_name if first_name else None)
            last_name = click.prompt("Last name", default=last_name if last_name else None)
            bedrooms = click.prompt("Preferred bedrooms (0-4)", type=int, default=bedrooms, show_default=False)
            move_date = click.prompt("Preferred move-in date (YYYY-MM-DD)", default=move_date, show_default=False)

        # Parse tour date
        try:
            parsed_tour_date = datetime.strptime(tour_date, "%Y-%m-%d %H:%M")
        except ValueError:
            click.echo("Error: Invalid tour date format. Use YYYY-MM-DD HH:MM", err=True)
            sys.exit(1)

        # Create request
        request_data = {
            "property_id": property_id,
            "renter_id": renter_id,
            "tour_date": parsed_tour_date,
            "first_name": first_name,
            "last_name": last_name
        }

        if bedrooms is not None:
            request_data["preference_bedrooms"] = bedrooms
        if move_date:
            request_data["preference_move_date"] = move_date

        try:
            request = ScheduleTourRequest(**request_data)
        except ValidationError as e:
            click.echo(f"Validation error: {e}", err=True)
            sys.exit(1)

        # Execute request
        async with MCPClient(config) as client:
            click.echo("Scheduling tour...")
            response = await client.schedule_tour(request)

            # Display results
            click.echo("\n" + "="*50)
            click.echo("TOUR SCHEDULING RESULT")
            click.echo("="*50)
            click.echo(f"Status: {response.status}")
            click.echo(f"Appointment ID: {response.appointment_id}")
            click.echo(f"Message: {response.message}")
            click.echo("="*50)

            if response.status == "success":
                click.echo("✅ Tour scheduled successfully!")
            else:
                click.echo("❌ Tour scheduling failed.")
                sys.exit(1)

    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option("--output", type=click.Path(), help="Output file for configuration template")
@click.pass_context
def generate_config(ctx, output):
    """Generate a configuration file template."""
    config_template = {
        "server_url": "http://localhost:8000",
        "mcp_path": "/mcp",
        "timeout": 30,
        "max_retries": 3,
        "log_level": "INFO"
    }

    output_file = output or "mcp_client_config.json"

    try:
        with open(output_file, 'w') as f:
            json.dump(config_template, f, indent=2)
        click.echo(f"Configuration template saved to: {output_file}")
    except Exception as e:
        click.echo(f"Error saving configuration: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
async def test_connection(ctx):
    """Test connection to the MCP server."""
    config = ctx.obj["config"]

    try:
        click.echo(f"Testing connection to {config.server_url}{config.mcp_path}...")

        async with MCPClient(config) as client:
            tools = client.list_tools()
            click.echo(f"✅ Connection successful! Found {len(tools)} tools.")

    except Exception as e:
        click.echo(f"❌ Connection failed: {e}", err=True)
        sys.exit(1)


def main():
    """Main entry point for the CLI application."""
    # Handle async commands
    def async_command(f):
        def wrapper(*args, **kwargs):
            return asyncio.run(f(*args, **kwargs))
        return wrapper

    # Apply async wrapper to async commands
    list_tools.callback = async_command(list_tools.callback)
    schedule_tour.callback = async_command(schedule_tour.callback)
    test_connection.callback = async_command(test_connection.callback)

    cli()


if __name__ == "__main__":
    main()
