[project]
name = "knock-mcp-server"
version = "0.0.1"
description = "A Python-based server application for the Knock MCP (Model Context Protocol) system"
requires-python = ">=3.11"
dependencies = [
    "fastapi[standard]>=0.109.0",
    "uvicorn>=0.27.0",
    "pydantic>=2.6.0",
    "pydantic-settings>=2.1.0",
    "mcp[cli]>=1.9.4",
    "httpx>=0.28.1",
    "fastapi-mcp>=0.3.4",
    "mcp-proxy>=0.8.0",
    "python-dotenv>=1.1.0",
    "opentelemetry-api>=1.34.1",
    "opentelemetry-sdk>=1.34.1",
    "opentelemetry-exporter-otlp>=1.34.1",
    "opentelemetry-instrumentation-fastapi>=0.55b1",
    "opentelemetry-instrumentation-httpx>=0.55b1",
    "opentelemetry-instrumentation-openai-v2>=2.1b0",
    "opentelemetry-instrumentation-requests>=0.55b1",
    "opentelemetry-instrumentation-aiohttp-client>=0.55b1",
    "openai>=1.93.0",
    "aiohttp>=3.12.13",
    "watchfiles>=1.1.0",
    "starlette>=0.46.2",
    "fastmcp>=2.10.4",
    "click>=8.2.1",
]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
[tool.ruff]
line-length = 120
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "UP",   # pyupgrade
    "N",    # pep8-naming
    "ARG",  # flake8-unused-arguments
    "PIE",  # flake8-pie
    "T20",  # flake8-print
    "TID",  # flake8-tidy-imports
    "Q",    # flake8-quotes
    "SIM",  # flake8-simplify
    "RET",  # flake8-return
    "SLF",  # flake8-self
    "SLOT", # flake8-slots
    "TRY",  # tryceratops
    "NPY",  # numpy-vet
    "AIR",  # flake8-airflow
    "PERF", # perflint
    "FURB", # refurb
    "LOG",  # flake8-logging-format
    "ERA",  # eradicate
    "PD",   # pandas-vet
    "PGH",  # pygrep-hooks
    "PL",   # pylint
    "RUF",  # ruff-specific rules
]
ignore = []

[tool.ruff.format]
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src --cov-report=term-missing"

[dependency-groups]
dev = [
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "mypy>=1.8.0",
    "ruff>=0.2.0",
    "pre-commit>=4.2.0",
]
