#!/bin/bash

# MCP CLI Client Runner Script
# This script provides convenient shortcuts for common MCP client operations

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLIENT_SCRIPT="$SCRIPT_DIR/mcp_cli_client.py"
CONFIG_FILE="$SCRIPT_DIR/mcp_config.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed or not in PATH"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    local major_version=$(echo $python_version | cut -d'.' -f1)
    local minor_version=$(echo $python_version | cut -d'.' -f2)
    
    if [ "$major_version" -lt 3 ] || ([ "$major_version" -eq 3 ] && [ "$minor_version" -lt 11 ]); then
        log_error "Python 3.11+ is required. Found: $python_version"
        exit 1
    fi
    
    log_info "Using Python $python_version"
}

# Check if dependencies are installed
check_dependencies() {
    local missing_deps=()
    
    for dep in aiohttp click pydantic; do
        if ! python3 -c "import $dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Install with: pip install ${missing_deps[*]}"
        exit 1
    fi
}

# Check if MCP server is running
check_server() {
    local server_url="${1:-http://localhost:8000}"
    
    log_info "Checking server at $server_url..."
    
    if curl -s -f "$server_url/api/health" > /dev/null 2>&1; then
        log_success "MCP server is running"
        return 0
    else
        log_warning "MCP server is not responding at $server_url"
        return 1
    fi
}

# Setup function
setup() {
    log_info "Setting up MCP CLI Client..."
    
    check_python
    
    # Install dependencies if needed
    log_info "Installing dependencies..."
    if command -v uv &> /dev/null; then
        uv add aiohttp click pydantic pydantic-settings
    else
        pip3 install aiohttp click pydantic pydantic-settings
    fi
    
    # Make client script executable
    chmod +x "$CLIENT_SCRIPT"
    
    # Generate config file if it doesn't exist
    if [ ! -f "$CONFIG_FILE" ]; then
        log_info "Generating configuration file..."
        python3 "$CLIENT_SCRIPT" generate-config --output "$CONFIG_FILE"
    fi
    
    log_success "Setup completed!"
}

# Test function
test() {
    log_info "Running MCP client tests..."
    
    check_python
    check_dependencies
    
    if ! check_server; then
        log_error "Please start the MCP server first: python main.py"
        exit 1
    fi
    
    python3 "$SCRIPT_DIR/test_client.py"
}

# Quick tour scheduling
quick_tour() {
    local property_id="$1"
    local renter_id="$2"
    local tour_date="$3"
    local first_name="$4"
    local last_name="$5"
    
    if [ -z "$property_id" ] || [ -z "$renter_id" ] || [ -z "$tour_date" ] || [ -z "$first_name" ] || [ -z "$last_name" ]; then
        log_error "Usage: $0 quick-tour <property_id> <renter_id> <tour_date> <first_name> <last_name>"
        log_info "Example: $0 quick-tour 12345 67890 '2024-12-25 14:30' John Doe"
        exit 1
    fi
    
    check_dependencies
    
    if ! check_server; then
        log_error "Please start the MCP server first: python main.py"
        exit 1
    fi
    
    log_info "Scheduling tour for $first_name $last_name..."
    
    python3 "$CLIENT_SCRIPT" schedule-tour \
        --property-id "$property_id" \
        --renter-id "$renter_id" \
        --tour-date "$tour_date" \
        --first-name "$first_name" \
        --last-name "$last_name"
}

# Interactive tour scheduling
interactive_tour() {
    check_dependencies
    
    if ! check_server; then
        log_error "Please start the MCP server first: python main.py"
        exit 1
    fi
    
    log_info "Starting interactive tour scheduling..."
    python3 "$CLIENT_SCRIPT" schedule-tour --interactive
}

# List tools
list_tools() {
    check_dependencies
    
    if ! check_server; then
        log_error "Please start the MCP server first: python main.py"
        exit 1
    fi
    
    python3 "$CLIENT_SCRIPT" list-tools
}

# Show help
show_help() {
    cat << EOF
MCP CLI Client Runner Script

Usage: $0 <command> [arguments]

Commands:
    setup                           Setup the MCP client (install dependencies, generate config)
    test                           Run the test suite
    check                          Check server connection and list tools
    quick-tour <args>              Schedule a tour with command line arguments
    interactive                    Schedule a tour in interactive mode
    list-tools                     List available tools from the server
    help                           Show this help message

Quick Tour Arguments:
    property_id                    Property ID (integer)
    renter_id                      Renter ID (integer)  
    tour_date                      Tour date and time (YYYY-MM-DD HH:MM)
    first_name                     First name
    last_name                      Last name

Examples:
    $0 setup
    $0 test
    $0 check
    $0 quick-tour 12345 67890 "2024-12-25 14:30" John Doe
    $0 interactive
    $0 list-tools

Configuration:
    The script uses $CONFIG_FILE for configuration.
    Generate with: $0 setup

Requirements:
    - Python 3.11+
    - MCP server running at http://localhost:8000
    - Dependencies: aiohttp, click, pydantic, pydantic-settings

EOF
}

# Main script logic
main() {
    case "${1:-help}" in
        setup)
            setup
            ;;
        test)
            test
            ;;
        check)
            check_dependencies
            check_server
            list_tools
            ;;
        quick-tour)
            shift
            quick_tour "$@"
            ;;
        interactive)
            interactive_tour
            ;;
        list-tools)
            list_tools
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
