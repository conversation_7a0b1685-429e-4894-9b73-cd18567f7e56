# MCP CLI Client Setup Guide

This guide provides step-by-step instructions for setting up and using the MCP CLI Client for Schedule Tour Tool.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Server Setup](#server-setup)
4. [Client Configuration](#client-configuration)
5. [First Run](#first-run)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Python**: 3.11 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 512MB RAM
- **Network**: Access to MCP server (default: localhost:8000)

### Check Python Version

```bash
python --version
# Should show Python 3.11.x or higher
```

If you don't have Python 3.11+, install it from [python.org](https://www.python.org/downloads/).

## Installation

### Option 1: Using uv (Recommended)

uv is a fast Python package installer and project manager.

#### Install uv

```bash
# On macOS and Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Alternative: using pip
pip install uv
```

#### Install Dependencies

```bash
# Navigate to the examples directory
cd examples

# Install dependencies
uv add aiohttp click pydantic pydantic-settings

# Make the script executable (Unix-like systems)
chmod +x mcp_cli_client.py
```

### Option 2: Using pip with Virtual Environment

#### Create Virtual Environment

```bash
# Navigate to the examples directory
cd examples

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

#### Install Dependencies

```bash
# Install from requirements file
pip install -r requirements.txt

# Or install individually
pip install aiohttp click pydantic pydantic-settings

# Make the script executable (Unix-like systems)
chmod +x mcp_cli_client.py
```

### Option 3: Using conda

```bash
# Create conda environment
conda create -n mcp-client python=3.11

# Activate environment
conda activate mcp-client

# Install dependencies
conda install -c conda-forge aiohttp click pydantic
pip install pydantic-settings  # Not available in conda-forge
```

## Server Setup

The MCP CLI client requires a running MCP server with the schedule tour tool.

### Start the MCP Server

From the project root directory:

```bash
# Using uv (recommended)
uv run python main.py

# Or using python directly
python main.py
```

The server will start on `http://localhost:8000` with the MCP endpoint at `/mcp`.

### Verify Server is Running

```bash
# Check if server is responding
curl http://localhost:8000/api/health

# Should return: {"status": "healthy"}
```

### Available Endpoints

- **Health Check**: `GET http://localhost:8000/api/health`
- **MCP Endpoint**: `POST http://localhost:8000/mcp`
- **Tour Scheduling**: `POST http://localhost:8000/tour/schedule`

## Client Configuration

### Method 1: Command Line Arguments

```bash
python mcp_cli_client.py \
  --server-url "http://localhost:8000" \
  --mcp-path "/mcp" \
  --timeout 30 \
  --log-level "INFO" \
  test-connection
```

### Method 2: Environment Variables

```bash
# Set environment variables
export MCP_SERVER_URL="http://localhost:8000"
export MCP_MCP_PATH="/mcp"
export MCP_TIMEOUT=30
export MCP_LOG_LEVEL="INFO"

# Run client
python mcp_cli_client.py test-connection
```

### Method 3: Configuration File

```bash
# Generate configuration template
python mcp_cli_client.py generate-config --output config.json

# Edit config.json as needed
{
  "server_url": "http://localhost:8000",
  "mcp_path": "/mcp",
  "timeout": 30,
  "max_retries": 3,
  "log_level": "INFO"
}

# Use configuration file
python mcp_cli_client.py --config-file config.json test-connection
```

## First Run

### Step 1: Test Connection

```bash
python mcp_cli_client.py test-connection
```

Expected output:
```
Testing connection to http://localhost:8000/mcp...
✅ Connection successful! Found 4 tools.
```

### Step 2: List Available Tools

```bash
python mcp_cli_client.py list-tools
```

Expected output:
```
Available tools:
  • schedule_property_tour: Schedule a property tour for a prospect renter.
  • pricing_and_availability: Check the pricing and availability for a given property and bedroom configuration.
  • update_prospect_guestcard: Update an existing prospect guestcard.
  • get_property_info: Get detailed information about a property.
```

### Step 3: Schedule Your First Tour

```bash
python mcp_cli_client.py schedule-tour \
  --property-id 12345 \
  --renter-id 67890 \
  --tour-date "2024-12-25 14:30" \
  --first-name "John" \
  --last-name "Doe" \
  --bedrooms 2 \
  --move-date "2025-01-15"
```

Expected output:
```
Scheduling tour...

==================================================
TOUR SCHEDULING RESULT
==================================================
Status: success
Appointment ID: apt_1234567890
Message: Tour scheduled for John Doe on 2024-12-25 at 14:30. Preferred bedrooms: 2. Preferred move date: 2025-01-15.
==================================================
✅ Tour scheduled successfully!
```

## Testing

### Run the Test Suite

```bash
# Run comprehensive tests
python test_client.py
```

Expected output:
```
MCP CLI Client Test Suite
==================================================
Testing against: http://localhost:8000/mcp

Validation Tests:
✅ Config Validation (Valid)
✅ Config Validation (Invalid)
✅ Request Validation (Valid)
✅ Request Validation (Invalid Property)
✅ Request Validation (Invalid Bedrooms)

Server Integration Tests:
✅ Connection
✅ Tool Discovery: Found 4 tools
✅ Schedule Tour (Valid): Appointment: apt_1234567890
✅ Schedule Tour (Minimal): Appointment: apt_0987654321
✅ Invalid Tool Handling

==================================================
SUMMARY
==================================================
Total Tests: 10
Passed: 10
Failed: 0

🎉 All tests passed!
```

### Run Example Usage

```bash
# Run example scenarios
python example_usage.py
```

This will demonstrate various usage patterns and error handling scenarios.

## Troubleshooting

### Common Issues and Solutions

#### 1. Connection Refused

**Error**: `❌ Connection failed: Connection refused`

**Solutions**:
- Ensure the MCP server is running: `python main.py`
- Check the server URL and port: `--server-url http://localhost:8000`
- Verify firewall settings allow connections to port 8000

#### 2. Module Not Found

**Error**: `ModuleNotFoundError: No module named 'aiohttp'`

**Solutions**:
- Install dependencies: `pip install -r requirements.txt`
- Activate virtual environment: `source venv/bin/activate`
- Use uv: `uv add aiohttp click pydantic pydantic-settings`

#### 3. Tool Not Found

**Error**: `Tool 'schedule_property_tour' not found`

**Solutions**:
- Check available tools: `python mcp_cli_client.py list-tools`
- Ensure server has tour tools registered
- Restart the MCP server

#### 4. Invalid Date Format

**Error**: `Invalid tour date format. Use YYYY-MM-DD HH:MM`

**Solutions**:
- Use correct format: `"2024-12-25 14:30"`
- Include both date and time
- Use 24-hour format for time

#### 5. Validation Errors

**Error**: `property_id must be greater than 0`

**Solutions**:
- Ensure property_id is a positive integer
- Check renter_id is a positive integer
- Verify bedrooms is between 0-4 if provided
- Use YYYY-MM-DD format for move_date

#### 6. Permission Denied

**Error**: `Permission denied: mcp_cli_client.py`

**Solutions**:
- Make script executable: `chmod +x mcp_cli_client.py`
- Run with python: `python mcp_cli_client.py`
- Check file permissions

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
python mcp_cli_client.py --log-level DEBUG test-connection
```

This will show:
- Detailed HTTP requests and responses
- MCP protocol messages
- Connection attempts and retries
- Internal client state

### Log Files

The client creates a log file `mcp_client.log` with detailed information:

```bash
# View recent log entries
tail -f mcp_client.log

# Search for errors
grep ERROR mcp_client.log
```

### Network Diagnostics

Test network connectivity:

```bash
# Test HTTP connectivity
curl -v http://localhost:8000/api/health

# Test MCP endpoint
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{}}'
```

### Getting Help

If you encounter issues not covered here:

1. Check the log files for detailed error messages
2. Run tests to isolate the problem: `python test_client.py`
3. Enable debug logging: `--log-level DEBUG`
4. Verify server is running and accessible
5. Check network connectivity and firewall settings

### Environment Information

Collect environment information for troubleshooting:

```bash
# Python version
python --version

# Installed packages
pip list | grep -E "(aiohttp|click|pydantic)"

# Network connectivity
curl -I http://localhost:8000

# Server status
python -c "import requests; print(requests.get('http://localhost:8000/api/health').json())"
```

## Next Steps

After successful setup:

1. **Explore Interactive Mode**: Use `--interactive` flag for guided input
2. **Automate with Scripts**: Integrate the client into your workflows
3. **Configure for Production**: Set up proper configuration files
4. **Monitor Logs**: Set up log monitoring for production use
5. **Scale Usage**: Use batch operations for multiple tours

For more advanced usage, see the main [README.md](README.md) file.
