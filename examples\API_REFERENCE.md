# MCP CLI Client API Reference

This document provides detailed API reference for the MCP CLI Client components.

## Table of Contents

1. [MCPClient Class](#mcpclient-class)
2. [Configuration Models](#configuration-models)
3. [Data Models](#data-models)
4. [Exception Classes](#exception-classes)
5. [CLI Commands](#cli-commands)
6. [Usage Examples](#usage-examples)

## MCPClient Class

The main client class for MCP communication.

### Constructor

```python
MCPClient(config: MCPConfig)
```

**Parameters:**
- `config`: MCPConfig instance with connection settings

### Context Manager Usage

```python
async with MC<PERSON>lient(config) as client:
    # Use client methods
    pass
```

### Methods

#### `connect() -> None`

Establish connection to the MCP server.

**Raises:**
- `MCPConnectionError`: If connection fails

#### `disconnect() -> None`

Close connection to the MCP server.

#### `call_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]`

Call a specific tool with arguments.

**Parameters:**
- `tool_name`: Name of the tool to call
- `arguments`: Dictionary of tool arguments

**Returns:**
- Dictionary containing tool response

**Raises:**
- `MCPToolError`: If tool not found or execution fails

#### `schedule_tour(request: ScheduleTourRequest) -> ScheduleTourResponse`

Schedule a property tour.

**Parameters:**
- `request`: ScheduleTourRequest instance

**Returns:**
- ScheduleTourResponse instance

**Raises:**
- `MCPToolError`: If scheduling fails
- `ValidationError`: If response format is invalid

#### `list_tools() -> List[str]`

Get list of available tool names.

**Returns:**
- List of tool names

#### `get_tool_info(tool_name: str) -> Optional[Dict[str, Any]]`

Get detailed information about a tool.

**Parameters:**
- `tool_name`: Name of the tool

**Returns:**
- Tool information dictionary or None if not found

## Configuration Models

### MCPConfig

Configuration settings for the MCP client.

```python
class MCPConfig(BaseSettings):
    server_url: str = "http://localhost:8000"
    mcp_path: str = "/mcp"
    timeout: int = 30
    max_retries: int = 3
    log_level: str = "INFO"
```

**Fields:**
- `server_url`: MCP server base URL
- `mcp_path`: MCP endpoint path
- `timeout`: Request timeout in seconds
- `max_retries`: Maximum retry attempts
- `log_level`: Logging level (DEBUG, INFO, WARNING, ERROR)

**Environment Variables:**
- `MCP_SERVER_URL`
- `MCP_MCP_PATH`
- `MCP_TIMEOUT`
- `MCP_MAX_RETRIES`
- `MCP_LOG_LEVEL`

## Data Models

### ScheduleTourRequest

Request model for scheduling tours.

```python
class ScheduleTourRequest(BaseModel):
    property_id: int
    renter_id: int
    tour_date: datetime
    first_name: str
    last_name: str
    preference_bedrooms: Optional[int] = None
    preference_move_date: Optional[str] = None
```

**Field Constraints:**
- `property_id`: Must be > 0
- `renter_id`: Must be > 0
- `first_name`: 1-50 characters
- `last_name`: 1-50 characters
- `preference_bedrooms`: 0-4 if provided
- `preference_move_date`: YYYY-MM-DD format if provided

### ScheduleTourResponse

Response model for tour scheduling.

```python
class ScheduleTourResponse(BaseModel):
    status: str
    appointment_id: str
    message: str
```

**Fields:**
- `status`: Status string (e.g., "success", "error")
- `appointment_id`: Unique appointment identifier
- `message`: Human-readable message

### MCP Protocol Models

#### MCPRequest

```python
class MCPRequest(BaseModel):
    jsonrpc: str = "2.0"
    id: Union[str, int]
    method: str
    params: Optional[Dict[str, Any]] = None
```

#### MCPResponse

```python
class MCPResponse(BaseModel):
    jsonrpc: str = "2.0"
    id: Union[str, int]
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
```

## Exception Classes

### MCPError

Base exception for all MCP-related errors.

```python
class MCPError(Exception):
    pass
```

### MCPConnectionError

Raised when connection to MCP server fails.

```python
class MCPConnectionError(MCPError):
    pass
```

**Common Causes:**
- Server not running
- Network connectivity issues
- Invalid server URL
- Firewall blocking connection

### MCPProtocolError

Raised when MCP protocol communication fails.

```python
class MCPProtocolError(MCPError):
    pass
```

**Common Causes:**
- Invalid MCP message format
- Server returned error response
- Protocol version mismatch

### MCPToolError

Raised when tool execution fails.

```python
class MCPToolError(MCPError):
    pass
```

**Common Causes:**
- Tool not found
- Invalid tool arguments
- Tool execution error
- Invalid response format

## CLI Commands

### Global Options

Available for all commands:

```bash
--server-url TEXT       MCP server URL [default: http://localhost:8000]
--mcp-path TEXT         MCP endpoint path [default: /mcp]
--timeout INTEGER       Request timeout in seconds [default: 30]
--max-retries INTEGER   Maximum number of retries [default: 3]
--log-level TEXT        Logging level [default: INFO]
--config-file PATH      Configuration file path
```

### test-connection

Test connection to the MCP server.

```bash
python mcp_cli_client.py test-connection
```

**Exit Codes:**
- 0: Success
- 1: Connection failed

### list-tools

List available tools from the MCP server.

```bash
python mcp_cli_client.py list-tools
```

**Output Format:**
```
Available tools:
  • tool_name: Tool description
```

### schedule-tour

Schedule a property tour.

```bash
python mcp_cli_client.py schedule-tour [OPTIONS]
```

**Required Options:**
- `--property-id INTEGER`: Property ID
- `--renter-id INTEGER`: Renter ID
- `--tour-date TEXT`: Tour date and time (YYYY-MM-DD HH:MM)
- `--first-name TEXT`: First name
- `--last-name TEXT`: Last name

**Optional Options:**
- `--bedrooms INTEGER`: Preferred bedrooms (0-4)
- `--move-date TEXT`: Preferred move-in date (YYYY-MM-DD)
- `--interactive`: Enable interactive mode

**Exit Codes:**
- 0: Tour scheduled successfully
- 1: Scheduling failed

### generate-config

Generate configuration file template.

```bash
python mcp_cli_client.py generate-config [--output PATH]
```

**Options:**
- `--output PATH`: Output file path [default: mcp_client_config.json]

## Usage Examples

### Basic Client Usage

```python
import asyncio
from mcp_cli_client import MCPClient, MCPConfig

async def main():
    config = MCPConfig(
        server_url="http://localhost:8000",
        log_level="INFO"
    )
    
    async with MCPClient(config) as client:
        tools = client.list_tools()
        print(f"Available tools: {tools}")

asyncio.run(main())
```

### Schedule Tour Programmatically

```python
import asyncio
from datetime import datetime, timedelta
from mcp_cli_client import MCPClient, MCPConfig, ScheduleTourRequest

async def schedule_tour_example():
    config = MCPConfig()
    
    request = ScheduleTourRequest(
        property_id=12345,
        renter_id=67890,
        tour_date=datetime.now() + timedelta(days=7),
        first_name="John",
        last_name="Doe",
        preference_bedrooms=2
    )
    
    async with MCPClient(config) as client:
        response = await client.schedule_tour(request)
        print(f"Tour scheduled: {response.appointment_id}")

asyncio.run(schedule_tour_example())
```

### Error Handling

```python
import asyncio
from mcp_cli_client import (
    MCPClient, MCPConfig, MCPConnectionError, 
    MCPToolError, ScheduleTourRequest
)

async def error_handling_example():
    config = MCPConfig(server_url="http://localhost:9999")  # Wrong port
    
    try:
        async with MCPClient(config) as client:
            tools = client.list_tools()
    except MCPConnectionError as e:
        print(f"Connection failed: {e}")
    except MCPToolError as e:
        print(f"Tool error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

asyncio.run(error_handling_example())
```

### Configuration from Environment

```python
import os
from mcp_cli_client import MCPConfig

# Set environment variables
os.environ["MCP_SERVER_URL"] = "https://api.example.com"
os.environ["MCP_LOG_LEVEL"] = "DEBUG"

# Configuration automatically loads from environment
config = MCPConfig()
print(f"Server URL: {config.server_url}")
print(f"Log Level: {config.log_level}")
```

### Batch Operations

```python
import asyncio
from datetime import datetime, timedelta
from mcp_cli_client import MCPClient, MCPConfig, ScheduleTourRequest

async def batch_schedule_tours():
    config = MCPConfig()
    
    tours = [
        ScheduleTourRequest(
            property_id=12345 + i,
            renter_id=67890 + i,
            tour_date=datetime.now() + timedelta(days=i+1),
            first_name=f"User{i}",
            last_name="Test"
        )
        for i in range(5)
    ]
    
    async with MCPClient(config) as client:
        results = []
        for tour in tours:
            try:
                response = await client.schedule_tour(tour)
                results.append(f"Success: {response.appointment_id}")
            except Exception as e:
                results.append(f"Failed: {e}")
        
        for result in results:
            print(result)

asyncio.run(batch_schedule_tours())
```

### Custom Tool Calls

```python
import asyncio
from mcp_cli_client import MCPClient, MCPConfig

async def custom_tool_example():
    config = MCPConfig()
    
    async with MCPClient(config) as client:
        # Call pricing and availability tool
        result = await client.call_tool(
            "pricing_and_availability",
            {
                "property_id": 12345,
                "bedrooms": "2",
                "move_date": "2025-01-15"
            }
        )
        print(f"Pricing result: {result}")

asyncio.run(custom_tool_example())
```

## Return Codes

### CLI Exit Codes

- **0**: Success
- **1**: General error (connection, validation, tool execution)

### HTTP Status Codes

The client handles these HTTP status codes from the MCP server:

- **200**: Success
- **400**: Bad Request (invalid MCP message)
- **404**: Not Found (invalid endpoint)
- **500**: Internal Server Error
- **503**: Service Unavailable

## Logging

### Log Levels

- **DEBUG**: Detailed protocol messages and HTTP requests
- **INFO**: General operation information
- **WARNING**: Non-fatal issues
- **ERROR**: Error conditions

### Log Format

```
2024-07-16 10:30:15,123 - module_name - LEVEL - Message
```

### Log Destinations

- **Console**: stdout for INFO and above
- **File**: `mcp_client.log` for all levels

## Performance Considerations

### Connection Pooling

The client uses aiohttp's connection pooling for efficient HTTP connections.

### Retry Logic

- Exponential backoff: 2^attempt seconds
- Maximum retries configurable via `max_retries`
- Only retries on network errors, not protocol errors

### Timeouts

- Request timeout: Configurable via `timeout` setting
- Connection timeout: 30 seconds (hardcoded)
- Total timeout: `timeout * (max_retries + 1)`

### Memory Usage

- Minimal memory footprint
- No persistent state beyond connection
- Automatic cleanup on context manager exit
