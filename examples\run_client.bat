@echo off
REM MCP CLI Client Runner Script for Windows
REM This script provides convenient shortcuts for common MCP client operations

setlocal enabledelayedexpansion

REM Configuration
set "SCRIPT_DIR=%~dp0"
set "CLIENT_SCRIPT=%SCRIPT_DIR%mcp_cli_client.py"
set "CONFIG_FILE=%SCRIPT_DIR%mcp_config.json"

REM Check if Python is available
:check_python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set "PYTHON_VERSION=%%i"
echo [INFO] Using Python %PYTHON_VERSION%

goto :main

REM Check if dependencies are installed
:check_dependencies
echo [INFO] Checking dependencies...
python -c "import aiohttp, click, pydantic" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Missing dependencies. Install with: pip install aiohttp click pydantic pydantic-settings
    exit /b 1
)
goto :eof

REM Check if MCP server is running
:check_server
set "SERVER_URL=%~1"
if "%SERVER_URL%"=="" set "SERVER_URL=http://localhost:8000"

echo [INFO] Checking server at %SERVER_URL%...
curl -s -f "%SERVER_URL%/api/health" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] MCP server is not responding at %SERVER_URL%
    exit /b 1
) else (
    echo [SUCCESS] MCP server is running
)
goto :eof

REM Setup function
:setup
echo [INFO] Setting up MCP CLI Client...

call :check_python

echo [INFO] Installing dependencies...
pip install aiohttp click pydantic pydantic-settings

REM Generate config file if it doesn't exist
if not exist "%CONFIG_FILE%" (
    echo [INFO] Generating configuration file...
    python "%CLIENT_SCRIPT%" generate-config --output "%CONFIG_FILE%"
)

echo [SUCCESS] Setup completed!
goto :eof

REM Test function
:test
echo [INFO] Running MCP client tests...

call :check_python
call :check_dependencies

call :check_server
if errorlevel 1 (
    echo [ERROR] Please start the MCP server first: python main.py
    exit /b 1
)

python "%SCRIPT_DIR%test_client.py"
goto :eof

REM Quick tour scheduling
:quick_tour
set "PROPERTY_ID=%~1"
set "RENTER_ID=%~2"
set "TOUR_DATE=%~3"
set "FIRST_NAME=%~4"
set "LAST_NAME=%~5"

if "%PROPERTY_ID%"=="" goto :quick_tour_usage
if "%RENTER_ID%"=="" goto :quick_tour_usage
if "%TOUR_DATE%"=="" goto :quick_tour_usage
if "%FIRST_NAME%"=="" goto :quick_tour_usage
if "%LAST_NAME%"=="" goto :quick_tour_usage

call :check_dependencies

call :check_server
if errorlevel 1 (
    echo [ERROR] Please start the MCP server first: python main.py
    exit /b 1
)

echo [INFO] Scheduling tour for %FIRST_NAME% %LAST_NAME%...

python "%CLIENT_SCRIPT%" schedule-tour --property-id "%PROPERTY_ID%" --renter-id "%RENTER_ID%" --tour-date "%TOUR_DATE%" --first-name "%FIRST_NAME%" --last-name "%LAST_NAME%"
goto :eof

:quick_tour_usage
echo [ERROR] Usage: %~nx0 quick-tour ^<property_id^> ^<renter_id^> ^<tour_date^> ^<first_name^> ^<last_name^>
echo [INFO] Example: %~nx0 quick-tour 12345 67890 "2024-12-25 14:30" John Doe
exit /b 1

REM Interactive tour scheduling
:interactive_tour
call :check_dependencies

call :check_server
if errorlevel 1 (
    echo [ERROR] Please start the MCP server first: python main.py
    exit /b 1
)

echo [INFO] Starting interactive tour scheduling...
python "%CLIENT_SCRIPT%" schedule-tour --interactive
goto :eof

REM List tools
:list_tools
call :check_dependencies

call :check_server
if errorlevel 1 (
    echo [ERROR] Please start the MCP server first: python main.py
    exit /b 1
)

python "%CLIENT_SCRIPT%" list-tools
goto :eof

REM Show help
:show_help
echo MCP CLI Client Runner Script for Windows
echo.
echo Usage: %~nx0 ^<command^> [arguments]
echo.
echo Commands:
echo     setup                           Setup the MCP client (install dependencies, generate config)
echo     test                           Run the test suite
echo     check                          Check server connection and list tools
echo     quick-tour ^<args^>              Schedule a tour with command line arguments
echo     interactive                    Schedule a tour in interactive mode
echo     list-tools                     List available tools from the server
echo     help                           Show this help message
echo.
echo Quick Tour Arguments:
echo     property_id                    Property ID (integer)
echo     renter_id                      Renter ID (integer)
echo     tour_date                      Tour date and time (YYYY-MM-DD HH:MM)
echo     first_name                     First name
echo     last_name                      Last name
echo.
echo Examples:
echo     %~nx0 setup
echo     %~nx0 test
echo     %~nx0 check
echo     %~nx0 quick-tour 12345 67890 "2024-12-25 14:30" John Doe
echo     %~nx0 interactive
echo     %~nx0 list-tools
echo.
echo Configuration:
echo     The script uses %CONFIG_FILE% for configuration.
echo     Generate with: %~nx0 setup
echo.
echo Requirements:
echo     - Python 3.11+
echo     - MCP server running at http://localhost:8000
echo     - Dependencies: aiohttp, click, pydantic, pydantic-settings
echo.
goto :eof

REM Check function
:check
call :check_dependencies
call :check_server
if not errorlevel 1 call :list_tools
goto :eof

REM Main script logic
:main
set "COMMAND=%~1"

if "%COMMAND%"=="" set "COMMAND=help"
if "%COMMAND%"=="setup" goto :setup
if "%COMMAND%"=="test" goto :test
if "%COMMAND%"=="check" goto :check
if "%COMMAND%"=="quick-tour" (
    shift
    call :quick_tour %1 %2 %3 %4 %5
    goto :eof
)
if "%COMMAND%"=="interactive" goto :interactive_tour
if "%COMMAND%"=="list-tools" goto :list_tools
if "%COMMAND%"=="help" goto :show_help
if "%COMMAND%"=="--help" goto :show_help
if "%COMMAND%"=="-h" goto :show_help

echo [ERROR] Unknown command: %COMMAND%
call :show_help
exit /b 1
