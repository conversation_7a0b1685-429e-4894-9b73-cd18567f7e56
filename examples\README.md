# MCP CLI Client for Schedule Tour Tool

A production-ready Python CLI client that connects to an MCP (Model Context Protocol) server via HTTP transport and provides access to the schedule tour functionality.

## Features

- **Async/await patterns** with aiohttp for efficient HTTP transport
- **Comprehensive error handling** for network issues, server errors, and invalid inputs
- **Flexible configuration** via CLI arguments, environment variables, and config files
- **Input validation** with Pydantic models for type safety
- **Interactive and non-interactive modes** for different use cases
- **Structured logging** with configurable levels
- **Connection management** with retry logic and exponential backoff
- **Tool discovery** and dynamic MCP protocol communication

## Prerequisites

- Python 3.11 or higher
- Access to a running MCP server (default: http://localhost:8000)

## Installation

### Option 1: Using uv (Recommended)

```bash
# Install dependencies using uv
uv add aiohttp click pydantic pydantic-settings

# Make the script executable
chmod +x examples/mcp_cli_client.py
```

### Option 2: Using pip

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install aiohttp click pydantic pydantic-settings

# Make the script executable
chmod +x examples/mcp_cli_client.py
```

## Quick Start

### 1. Start the MCP Server

First, ensure your MCP server is running:

```bash
# From the project root directory
uv run python main.py
```

The server will be available at `http://localhost:8000` with MCP endpoint at `/mcp`.

### 2. Test Connection

```bash
# Test connection to the MCP server
python examples/mcp_cli_client.py test-connection
```

### 3. List Available Tools

```bash
# Discover available tools
python examples/mcp_cli_client.py list-tools
```

### 4. Schedule a Tour

```bash
# Schedule a tour with all required parameters
python examples/mcp_cli_client.py schedule-tour \
  --property-id 12345 \
  --renter-id 67890 \
  --tour-date "2024-12-25 14:30" \
  --first-name "John" \
  --last-name "Doe" \
  --bedrooms 2 \
  --move-date "2025-01-15"
```

## Configuration

### Command Line Arguments

All configuration options can be provided via command line:

```bash
python examples/mcp_cli_client.py \
  --server-url "http://localhost:8000" \
  --mcp-path "/mcp" \
  --timeout 30 \
  --max-retries 3 \
  --log-level "INFO" \
  schedule-tour [options]
```

### Environment Variables

Set environment variables with the `MCP_` prefix:

```bash
export MCP_SERVER_URL="http://localhost:8000"
export MCP_MCP_PATH="/mcp"
export MCP_TIMEOUT=30
export MCP_MAX_RETRIES=3
export MCP_LOG_LEVEL="INFO"
```

### Configuration File

Generate and use a configuration file:

```bash
# Generate configuration template
python examples/mcp_cli_client.py generate-config --output my_config.json

# Use configuration file
python examples/mcp_cli_client.py --config-file my_config.json schedule-tour [options]
```

Example configuration file (`my_config.json`):

```json
{
  "server_url": "http://localhost:8000",
  "mcp_path": "/mcp",
  "timeout": 30,
  "max_retries": 3,
  "log_level": "INFO"
}
```

## Usage Examples

### Interactive Mode

Use interactive mode for guided input:

```bash
python examples/mcp_cli_client.py schedule-tour --interactive
```

This will prompt you for each required field:

```
Property ID: 12345
Renter ID: 67890
Tour date and time (YYYY-MM-DD HH:MM): 2024-12-25 14:30
First name: John
Last name: Doe
Preferred bedrooms (0-4): 2
Preferred move-in date (YYYY-MM-DD): 2025-01-15
```

### Non-Interactive Mode

Provide all parameters via command line:

```bash
python examples/mcp_cli_client.py schedule-tour \
  --property-id 12345 \
  --renter-id 67890 \
  --tour-date "2024-12-25 14:30" \
  --first-name "John" \
  --last-name "Doe" \
  --bedrooms 2 \
  --move-date "2025-01-15"
```

### Minimal Required Parameters

Only required fields:

```bash
python examples/mcp_cli_client.py schedule-tour \
  --property-id 12345 \
  --renter-id 67890 \
  --tour-date "2024-12-25 14:30" \
  --first-name "John" \
  --last-name "Doe"
```

### Different Server Configuration

Connect to a different MCP server:

```bash
python examples/mcp_cli_client.py \
  --server-url "https://api.example.com" \
  --mcp-path "/api/mcp" \
  --timeout 60 \
  schedule-tour [options]
```

## Command Reference

### Global Options

- `--server-url`: MCP server URL (default: http://localhost:8000)
- `--mcp-path`: MCP endpoint path (default: /mcp)
- `--timeout`: Request timeout in seconds (default: 30)
- `--max-retries`: Maximum number of retries (default: 3)
- `--log-level`: Logging level (default: INFO)
- `--config-file`: Path to configuration file

### Commands

#### `test-connection`

Test connection to the MCP server.

```bash
python examples/mcp_cli_client.py test-connection
```

#### `list-tools`

List all available tools from the MCP server.

```bash
python examples/mcp_cli_client.py list-tools
```

#### `schedule-tour`

Schedule a property tour.

**Required Options:**
- `--property-id`: Property ID (integer)
- `--renter-id`: Renter ID (integer)
- `--tour-date`: Tour date and time (YYYY-MM-DD HH:MM)
- `--first-name`: First name (string)
- `--last-name`: Last name (string)

**Optional Options:**
- `--bedrooms`: Preferred number of bedrooms (0-4)
- `--move-date`: Preferred move-in date (YYYY-MM-DD)
- `--interactive`: Enable interactive mode

#### `generate-config`

Generate a configuration file template.

```bash
python examples/mcp_cli_client.py generate-config --output config.json
```

## Error Handling

The client provides comprehensive error handling with user-friendly messages:

### Connection Errors

```
❌ Connection failed: Connection refused
```

### Validation Errors

```
Validation error: property_id must be greater than 0
```

### Tool Execution Errors

```
Error: Tool 'schedule_property_tour' not found. Available tools: ['pricing_and_availability', 'update_prospect_guestcard']
```

### Protocol Errors

```
Error: MCP error: {'code': -32601, 'message': 'Method not found'}
```

## Logging

The client logs to both console and file (`mcp_client.log`):

```
2024-07-16 10:30:15,123 - __main__ - INFO - Connected to MCP server at http://localhost:8000/mcp
2024-07-16 10:30:15,456 - __main__ - INFO - Discovered 4 tools: ['schedule_property_tour', 'pricing_and_availability', 'update_prospect_guestcard', 'get_property_info']
2024-07-16 10:30:16,789 - __main__ - INFO - Calling tool 'schedule_property_tour' with arguments: {'property_id': 12345, ...}
2024-07-16 10:30:17,012 - __main__ - INFO - Tool 'schedule_property_tour' executed successfully
```

Configure log level:

```bash
python examples/mcp_cli_client.py --log-level DEBUG schedule-tour [options]
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure the MCP server is running
   - Check the server URL and port
   - Verify firewall settings

2. **Tool Not Found**
   - Use `list-tools` to see available tools
   - Ensure the server has the schedule tour tool registered

3. **Invalid Date Format**
   - Use the format: YYYY-MM-DD HH:MM
   - Example: "2024-12-25 14:30"

4. **Validation Errors**
   - Check that property_id and renter_id are positive integers
   - Ensure bedrooms is between 0-4 if provided
   - Verify move_date format is YYYY-MM-DD

### Debug Mode

Enable debug logging for detailed information:

```bash
python examples/mcp_cli_client.py --log-level DEBUG schedule-tour [options]
```

This will show detailed MCP protocol messages and HTTP requests.

## Development

### Running Tests

```bash
# Test connection
python examples/mcp_cli_client.py test-connection

# Test tool discovery
python examples/mcp_cli_client.py list-tools

# Test tour scheduling with sample data
python examples/mcp_cli_client.py schedule-tour \
  --property-id 12345 \
  --renter-id 67890 \
  --tour-date "2024-12-25 14:30" \
  --first-name "Test" \
  --last-name "User"
```

### Code Structure

- **MCPClient**: Core async client for MCP communication
- **MCPConfig**: Configuration management with Pydantic
- **ScheduleTourRequest/Response**: Data models for tour scheduling
- **CLI Commands**: Click-based command line interface
- **Error Handling**: Custom exception hierarchy

## License

This project is part of the Knock MCP Server and follows the same licensing terms.
