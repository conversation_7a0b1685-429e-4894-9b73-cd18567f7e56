#!/usr/bin/env python3
"""
Test script for the MCP CLI Client

This script provides basic tests to validate the MCP client functionality.
Run this after starting the MCP server to ensure everything works correctly.
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add the examples directory to the path so we can import the client
sys.path.insert(0, str(Path(__file__).parent))

from mcp_cli_client import MCPClient, MCPConfig, ScheduleTourRequest, MCPConnectionError, MCPToolError


class TestResults:
    """Simple test results tracker."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.tests = []
    
    def add_test(self, name: str, passed: bool, message: str = ""):
        self.tests.append((name, passed, message))
        if passed:
            self.passed += 1
            print(f"✅ {name}")
        else:
            self.failed += 1
            print(f"❌ {name}: {message}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\nTest Results: {self.passed}/{total} passed")
        if self.failed > 0:
            print("Failed tests:")
            for name, passed, message in self.tests:
                if not passed:
                    print(f"  - {name}: {message}")


async def test_connection(config: MCPConfig, results: TestResults):
    """Test basic connection to MCP server."""
    try:
        async with MCPClient(config) as client:
            # Just connecting and disconnecting
            pass
        results.add_test("Connection", True)
    except Exception as e:
        results.add_test("Connection", False, str(e))


async def test_tool_discovery(config: MCPConfig, results: TestResults):
    """Test tool discovery functionality."""
    try:
        async with MCPClient(config) as client:
            tools = client.list_tools()
            
            if not tools:
                results.add_test("Tool Discovery", False, "No tools found")
                return
            
            if "schedule_property_tour" not in tools:
                results.add_test("Tool Discovery", False, "schedule_property_tour not found")
                return
            
            tool_info = client.get_tool_info("schedule_property_tour")
            if not tool_info:
                results.add_test("Tool Discovery", False, "No tool info available")
                return
            
            results.add_test("Tool Discovery", True, f"Found {len(tools)} tools")
            
    except Exception as e:
        results.add_test("Tool Discovery", False, str(e))


async def test_schedule_tour_valid(config: MCPConfig, results: TestResults):
    """Test valid tour scheduling."""
    try:
        async with MCPClient(config) as client:
            # Create a valid request
            tour_date = datetime.now() + timedelta(days=7)
            request = ScheduleTourRequest(
                property_id=12345,
                renter_id=67890,
                tour_date=tour_date,
                first_name="Test",
                last_name="User",
                preference_bedrooms=2,
                preference_move_date="2025-01-15"
            )
            
            response = await client.schedule_tour(request)
            
            if response.status and response.appointment_id:
                results.add_test("Schedule Tour (Valid)", True, f"Appointment: {response.appointment_id}")
            else:
                results.add_test("Schedule Tour (Valid)", False, "Invalid response format")
                
    except Exception as e:
        results.add_test("Schedule Tour (Valid)", False, str(e))


async def test_schedule_tour_minimal(config: MCPConfig, results: TestResults):
    """Test tour scheduling with minimal required fields."""
    try:
        async with MCPClient(config) as client:
            # Create a minimal request
            tour_date = datetime.now() + timedelta(days=8)
            request = ScheduleTourRequest(
                property_id=54321,
                renter_id=98765,
                tour_date=tour_date,
                first_name="Minimal",
                last_name="Test"
            )
            
            response = await client.schedule_tour(request)
            
            if response.status and response.appointment_id:
                results.add_test("Schedule Tour (Minimal)", True, f"Appointment: {response.appointment_id}")
            else:
                results.add_test("Schedule Tour (Minimal)", False, "Invalid response format")
                
    except Exception as e:
        results.add_test("Schedule Tour (Minimal)", False, str(e))


async def test_invalid_tool(config: MCPConfig, results: TestResults):
    """Test calling an invalid tool."""
    try:
        async with MCPClient(config) as client:
            try:
                await client.call_tool("nonexistent_tool", {})
                results.add_test("Invalid Tool Handling", False, "Should have raised an error")
            except MCPToolError:
                results.add_test("Invalid Tool Handling", True)
            except Exception as e:
                results.add_test("Invalid Tool Handling", False, f"Wrong exception type: {e}")
                
    except Exception as e:
        results.add_test("Invalid Tool Handling", False, str(e))


async def test_configuration_validation():
    """Test configuration validation."""
    results = TestResults()
    
    # Test valid configuration
    try:
        config = MCPConfig(
            server_url="http://localhost:8000",
            mcp_path="/mcp",
            timeout=30
        )
        results.add_test("Config Validation (Valid)", True)
    except Exception as e:
        results.add_test("Config Validation (Valid)", False, str(e))
    
    # Test invalid timeout
    try:
        config = MCPConfig(timeout=-1)
        results.add_test("Config Validation (Invalid)", False, "Should reject negative timeout")
    except Exception:
        results.add_test("Config Validation (Invalid)", True)
    
    return results


async def test_request_validation():
    """Test request validation."""
    results = TestResults()
    
    # Test valid request
    try:
        request = ScheduleTourRequest(
            property_id=12345,
            renter_id=67890,
            tour_date=datetime.now() + timedelta(days=1),
            first_name="Test",
            last_name="User"
        )
        results.add_test("Request Validation (Valid)", True)
    except Exception as e:
        results.add_test("Request Validation (Valid)", False, str(e))
    
    # Test invalid property_id
    try:
        request = ScheduleTourRequest(
            property_id=0,  # Invalid
            renter_id=67890,
            tour_date=datetime.now() + timedelta(days=1),
            first_name="Test",
            last_name="User"
        )
        results.add_test("Request Validation (Invalid Property)", False, "Should reject zero property_id")
    except Exception:
        results.add_test("Request Validation (Invalid Property)", True)
    
    # Test invalid bedrooms
    try:
        request = ScheduleTourRequest(
            property_id=12345,
            renter_id=67890,
            tour_date=datetime.now() + timedelta(days=1),
            first_name="Test",
            last_name="User",
            preference_bedrooms=10  # Invalid (max is 4)
        )
        results.add_test("Request Validation (Invalid Bedrooms)", False, "Should reject bedrooms > 4")
    except Exception:
        results.add_test("Request Validation (Invalid Bedrooms)", True)
    
    return results


async def main():
    """Run all tests."""
    print("MCP CLI Client Test Suite")
    print("=" * 50)
    
    # Configuration for tests
    config = MCPConfig(
        server_url="http://localhost:8000",
        mcp_path="/mcp",
        timeout=10,
        max_retries=2,
        log_level="WARNING"  # Reduce log noise during tests
    )
    
    print(f"Testing against: {config.server_url}{config.mcp_path}")
    print()
    
    # Run validation tests (don't require server)
    print("Validation Tests:")
    config_results = await test_configuration_validation()
    request_results = await test_request_validation()
    
    print("\nServer Integration Tests:")
    
    # Run server integration tests
    server_results = TestResults()
    
    await test_connection(config, server_results)
    await test_tool_discovery(config, server_results)
    await test_schedule_tour_valid(config, server_results)
    await test_schedule_tour_minimal(config, server_results)
    await test_invalid_tool(config, server_results)
    
    # Combined results
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    total_passed = config_results.passed + request_results.passed + server_results.passed
    total_failed = config_results.failed + request_results.failed + server_results.failed
    total_tests = total_passed + total_failed
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {total_passed}")
    print(f"Failed: {total_failed}")
    
    if total_failed == 0:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print(f"\n⚠️  {total_failed} test(s) failed.")
        
        # Show failed tests
        all_results = [config_results, request_results, server_results]
        for result_set in all_results:
            for name, passed, message in result_set.tests:
                if not passed:
                    print(f"  ❌ {name}: {message}")
        
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nTests interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")
        sys.exit(1)
